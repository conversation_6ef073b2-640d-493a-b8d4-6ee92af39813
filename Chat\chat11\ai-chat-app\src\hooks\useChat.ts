'use client';

import { useState, useEffect, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Conversation, Message, ChatState } from '@/types/chat';
import { StorageService } from '@/lib/storage';
import { APIService } from '@/lib/api';

export function useChat() {
  const [state, setState] = useState<ChatState>({
    conversations: [],
    currentConversationId: null,
    isLoading: false,
    error: null,
    sidebarOpen: false
  });

  // Load conversations from localStorage on mount
  useEffect(() => {
    const conversations = StorageService.loadConversations();
    const currentConversationId = StorageService.loadCurrentConversationId();
    
    setState(prev => ({
      ...prev,
      conversations,
      currentConversationId: conversations.find(c => c.id === currentConversationId) ? currentConversationId : null
    }));
  }, []);

  const getCurrentConversation = useCallback((): Conversation | null => {
    if (!state.currentConversationId) return null;
    return state.conversations.find(c => c.id === state.currentConversationId) || null;
  }, [state.conversations, state.currentConversationId]);

  const createNewConversation = useCallback((title?: string): string => {
    const newConversation: Conversation = {
      id: uuidv4(),
      title: title || 'New Chat',
      messages: [],
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    setState(prev => ({
      ...prev,
      conversations: [newConversation, ...prev.conversations],
      currentConversationId: newConversation.id,
      error: null
    }));

    StorageService.updateConversation(newConversation);
    StorageService.saveCurrentConversationId(newConversation.id);

    return newConversation.id;
  }, []);

  const selectConversation = useCallback((id: string) => {
    setState(prev => ({
      ...prev,
      currentConversationId: id,
      error: null
    }));
    StorageService.saveCurrentConversationId(id);
  }, []);

  const deleteConversation = useCallback((id: string) => {
    setState(prev => {
      const newConversations = prev.conversations.filter(c => c.id !== id);
      const newCurrentId = prev.currentConversationId === id 
        ? (newConversations.length > 0 ? newConversations[0].id : null)
        : prev.currentConversationId;

      return {
        ...prev,
        conversations: newConversations,
        currentConversationId: newCurrentId,
        error: null
      };
    });

    StorageService.deleteConversation(id);
  }, []);

  const renameConversation = useCallback((id: string, newTitle: string) => {
    setState(prev => ({
      ...prev,
      conversations: prev.conversations.map(c => 
        c.id === id 
          ? { ...c, title: newTitle, updatedAt: Date.now() }
          : c
      )
    }));

    const conversation = state.conversations.find(c => c.id === id);
    if (conversation) {
      const updatedConversation = { ...conversation, title: newTitle, updatedAt: Date.now() };
      StorageService.updateConversation(updatedConversation);
    }
  }, [state.conversations]);

  const updateConversation = useCallback((conversation: Conversation) => {
    setState(prev => ({
      ...prev,
      conversations: prev.conversations.map(c => 
        c.id === conversation.id ? conversation : c
      )
    }));
    StorageService.updateConversation(conversation);
  }, []);

  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim()) return;

    let conversationId = state.currentConversationId;
    
    // Create new conversation if none exists
    if (!conversationId) {
      conversationId = createNewConversation();
    }

    const userMessage: Message = {
      id: uuidv4(),
      role: 'user',
      content: content.trim(),
      timestamp: Date.now()
    };

    const assistantMessage: Message = {
      id: uuidv4(),
      role: 'assistant',
      content: '',
      timestamp: Date.now(),
      isStreaming: true
    };

    // Add user message and placeholder assistant message
    setState(prev => {
      const conversation = prev.conversations.find(c => c.id === conversationId);
      if (!conversation) return prev;

      const updatedConversation = {
        ...conversation,
        messages: [...conversation.messages, userMessage, assistantMessage],
        updatedAt: Date.now(),
        title: conversation.messages.length === 0 ? content.slice(0, 50) : conversation.title
      };

      return {
        ...prev,
        conversations: prev.conversations.map(c => 
          c.id === conversationId ? updatedConversation : c
        ),
        isLoading: true,
        error: null
      };
    });

    try {
      const conversation = state.conversations.find(c => c.id === conversationId);
      if (!conversation) return;

      const messages = [...conversation.messages, userMessage].map(msg => ({
        role: msg.role as 'user' | 'assistant' | 'system',
        content: msg.content
      }));

      let fullResponse = '';

      await APIService.sendMessage(
        messages,
        // onChunk
        (chunk: string) => {
          fullResponse += chunk;
          setState(prev => ({
            ...prev,
            conversations: prev.conversations.map(c => 
              c.id === conversationId 
                ? {
                    ...c,
                    messages: c.messages.map(m => 
                      m.id === assistantMessage.id 
                        ? { ...m, content: fullResponse }
                        : m
                    )
                  }
                : c
            )
          }));
        },
        // onComplete
        () => {
          setState(prev => ({
            ...prev,
            conversations: prev.conversations.map(c => 
              c.id === conversationId 
                ? {
                    ...c,
                    messages: c.messages.map(m => 
                      m.id === assistantMessage.id 
                        ? { ...m, isStreaming: false }
                        : m
                    ),
                    updatedAt: Date.now()
                  }
                : c
            ),
            isLoading: false
          }));

          // Save updated conversation
          const updatedConversation = state.conversations.find(c => c.id === conversationId);
          if (updatedConversation) {
            StorageService.updateConversation({
              ...updatedConversation,
              messages: updatedConversation.messages.map(m => 
                m.id === assistantMessage.id 
                  ? { ...m, content: fullResponse, isStreaming: false }
                  : m
              ),
              updatedAt: Date.now()
            });
          }
        },
        // onError
        (error: string) => {
          setState(prev => ({
            ...prev,
            conversations: prev.conversations.map(c => 
              c.id === conversationId 
                ? {
                    ...c,
                    messages: c.messages.filter(m => m.id !== assistantMessage.id)
                  }
                : c
            ),
            isLoading: false,
            error
          }));
        }
      );
    } catch (error) {
      setState(prev => ({
        ...prev,
        conversations: prev.conversations.map(c => 
          c.id === conversationId 
            ? {
                ...c,
                messages: c.messages.filter(m => m.id !== assistantMessage.id)
              }
            : c
        ),
        isLoading: false,
        error: error instanceof Error ? error.message : 'An error occurred'
      }));
    }
  }, [state.currentConversationId, state.conversations, createNewConversation]);

  const toggleSidebar = useCallback(() => {
    setState(prev => ({ ...prev, sidebarOpen: !prev.sidebarOpen }));
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    ...state,
    currentConversation: getCurrentConversation(),
    createNewConversation,
    selectConversation,
    deleteConversation,
    renameConversation,
    sendMessage,
    toggleSidebar,
    clearError
  };
}
